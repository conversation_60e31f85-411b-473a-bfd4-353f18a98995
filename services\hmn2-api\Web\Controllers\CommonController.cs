using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Data;
using System.Text.Json;
using Web.Models.Controller.Common;
using Web.Models.Controller;
using Web.Models.Service;
using Web.Repository.Models.Entities;
using Web.Services;
using Web.Services.Interfaces;
using Web.Models.Service.Preference;
using Web.Constant;
using Web.Validation;

namespace Web.Controller;

/// <summary>
/// 工具控制器
/// </summary>
[Route("[controller]")]
[Authorize]
public class CommonController(IDataAccessService dataAccessService,
                               IConfigurationService configurationService,
                               IBusinessService businessService,
                               IFieldService fieldService,
                               IPreferenceService preferenceService,
                               ICredentialService credentialService,
                               IRequestContextService requestContextService,
                               ILogService logSercice) : BaseController(dataAccessService, credentialService, requestContextService, logSercice)
{
    private readonly IConfigurationService _configurationService = configurationService;
    private readonly IBusinessService _businessService = businessService;
    private readonly IPreferenceService _preferenceService = preferenceService;
    private readonly IFieldService _fieldService = fieldService;
    private const string FileStorageBasePath = "FileStorage";

    /// <summary>
    /// 取得所有查詢視窗的下拉選單資料
    /// </summary>
    /// <param name="param"></param>
    /// <returns></returns>
    [HttpGet("comboData")]
    public async Task<IActionResult> GetComboData(GetComboData param)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        string appCode = _user.AppCode;
        List<string> areaCodeList = _user.AreaCodeList.ToList();
        string areaCode = param.AreaCode;

        bool is250DepartControl = bool.Parse(await _preferenceService.FetchGlobalParameter(appCode, "250DepartControl"));

        // 順序加載數據
        // 棟別
        var buildingList = await _dataAccessService.Fetch<Building>(e => e.AppCode == appCode && e.AreaCode == areaCode).Where(e => e.Enable).ToListAsync();
        var buildingCodeList = buildingList.Select(x => x.BuildingCode).ToList();

        // 裝置
        var deviceList = await _dataAccessService.Fetch<Device>(e => e.AppCode == appCode && e.AreaCode == areaCode).Where(e => e.Active && e.Enable).ToListAsync();

        // 裝置類型
        var deviceTypeList = await _configurationService.FetchSupportDeviceTypeList(appCode, areaCodeList);

        // 對象
        var objectList = await _dataAccessService.Fetch<ObjectDatum>(e => e.AppCode == appCode && e.AreaCode == areaCode && e.Enable && e.Active).ToListAsync();

        // 對象群組
        var objectGroupList = await _dataAccessService.Fetch<ObjectGroup>(e => e.AppCode == appCode && e.AreaCode == areaCode && e.Enable).ToListAsync();

        // 對象類型
        var objectTypeList = await _dataAccessService.Fetch<ObjectType>(e => e.AppCode == appCode && e.Enable).ToListAsync();

        // 樓層
        var planeList = await _dataAccessService.Fetch<Plane>(x => x.AppCode == appCode && buildingCodeList.Contains(x.BuildingCode) && x.Enable == true).Select(x => new Plane
        {
            PlaneId = x.PlaneId,
            AppCode = x.AppCode,
            BuildingCode = x.BuildingCode,
            PlaneCode = x.PlaneCode,
            PlaneName = x.PlaneName,
            Enable = x.Enable,
            PlaneNo = x.PlaneNo,
            PlaneMapPath = $"{x.PlaneMapPath}",
            MapWidth = x.MapWidth,
            MapHeight = x.MapHeight,
            PositionX = x.PositionX,
            PositionY = x.PositionY,
            ModifyDate = x.ModifyDate
        }).ToListAsync();
        var planeCodeList = planeList.Select(x => x.PlaneCode).ToList();

        // 位置（因為需要樓層資料，所以放在樓層後面）
        var locationList = await _dataAccessService.Fetch<Location>(x => x.AppCode == appCode && planeCodeList.Contains(x.PlaneCode) && x.Enable == true).ToListAsync();

        // 次平面
        var sectorList = _dataAccessService.Fetch<Sector>(x => x.AppCode == appCode && planeCodeList.Contains(x.PlaneCode) && x.Enable == true).Select(x => new Sector
        {
            SectorId = x.SectorId,
            AppCode = x.AppCode,
            PlaneCode = x.PlaneCode,
            SectorCode = x.SectorCode,
            CustomSectorCode = x.CustomSectorCode,
            SectorName = x.SectorName,
            Enable = x.Enable,
            SectorMapPath = $"{x.SectorMapPath}",
            MapWidth = x.MapWidth,
            MapHeight = x.MapHeight,
            PositionX = x.PositionX,
            PositionY = x.PositionY,
            ModifyDate = x.ModifyDate
        });

        // 取出所有歸屬於deptList的次平面資料
        var departSectorList = await _dataAccessService.Fetch<DepartSector>(x => x.AppCode == appCode && deptList.Any(d => d.AreaCode == x.AreaCode && d.DeptCode == x.DeptCode) && sectorList.Any(s => s.SectorCode == x.SectorCode)).ToListAsync();

        // 單位
        IQueryable<Department> deptTmpList = await _businessService.FetchDepartmentsByPermission(appCode, areaCode, param.Check250DepartControl);
        var deptList = 


        // 取出所有內定編碼
        var sysCodeList = _dataAccessService.Fetch<SysCode>(e => e.Enable == true).Select(x => new
        {
            x.Code,
            x.CodeType,
            x.Extend,
            Name = x.StringId == null ? x.Name : $"${x.StringId}",
            x.Sort,
            x.ValueType
        }).OrderBy(x => x.Sort);

        // 取出所有角色
        var roleList = _dataAccessService.Fetch<Role>(e => e.AppCode == appCode && e.Enable == true).Select(x => new
        {
            x.RoleCode,
            x.RoleName,
            x.RoleDesc,
            x.Enable
        }).OrderBy(x => x.RoleName);

        var result = new ReturnModel
        {
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = new
            {
                DeptControl = is250DepartControl,
                Check250DepartControl = param.Check250DepartControl,
                BuildingList = buildingList,
                DeptList = await deptList.ToListAsync(),
                DeptSectorList = departSectorList,
                DeviceList = deviceList,
                DeviceTypeList = deviceTypeList,
                LocationList = locationList,
                ObjectGroupList = objectGroupList,
                ObjectList = objectList,
                ObjectTypeList = objectTypeList,
                PlaneList = planeList,
                SectorList = await sectorList.ToListAsync(),
                SysCodeList = await sysCodeList.ToListAsync(),
                RoleList = await roleList.ToListAsync()
            },
            authorize = (Authorize)_user
        };

        _logService.Logging("info", logActionName, requestUUID, result.ToString());

        _logService.Logging("info", logActionName, requestUUID, "End");

        return Ok(result);
    }

    /// <summary>
    /// 取得指定顯示的系統名稱
    /// </summary>
    /// <remarks>
    /// 此method登入頁會呼叫，故指定可匿名存取（尚未認證狀態）
    /// </remarks>
    /// <returns></returns>
    [AllowAnonymous]
    [HttpGet("systemName")]
    public async Task<IActionResult> GetSystemName()
    {
        string appCode = _user.AppCode;

        var systemParam = _dataAccessService.Fetch<SysParameters>(e => e.ParaCode == "SystemName")
                                                .FirstOrDefault();

        var globalParam = _dataAccessService.Fetch<GlobalSysPara>(e => e.AppCode == appCode && e.ParaCode == "SystemName" && e.AppCode == appCode)
        .FirstOrDefault();

        var result = globalParam?.ParaValue ?? systemParam?.ParaValue ?? string.Empty;

        //TODO 多國語言要重構
        var currentCulture = Thread.CurrentThread.CurrentUICulture.Name;

        var language = await _dataAccessService.Fetch<Locale>(x => x.Locale1 == currentCulture).FirstOrDefaultAsync();

        var systemNameInfo = JsonSerializer.Deserialize<Dictionary<string, string>>(result);

        var systemName = language != null && systemNameInfo != null && systemNameInfo.TryGetValue(language.Locale1, out var name)
            ? name
            : string.Empty;

        ReturnModel returnResult = new()
        {
            result = true,
            data = new { title = systemName },
            authorize = new Authorize
            {
                Account = _user.Account
            }
        };

        return Ok(returnResult);
    }
    /// <summary>
    /// 可以一次取得GlobalSysPara所有參數
    /// </summary>
    /// <returns></returns>
    [HttpGet("sysPara")]
    public async Task<IActionResult> GetAllParameter(InGetAllParameter param)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        ReturnModel returnModel;

        var result = await _preferenceService.FetchSysParameter(_user.AppCode);

        returnModel = new ReturnModel
        {
            requestUUID = requestUUID,
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = result
        };

        return Ok(returnModel);
    }

    /// <summary>
    /// 更新SysPara參數
    /// </summary>
    /// <returns></returns>
    [HttpPatch("sysPara")]
    public async Task<IActionResult> UpdateParameter([FromBody] UpdateSysParameter param)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string appCode = _user.AppCode;
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        string planeMapPath = "";
        string fileName = Guid.NewGuid().ToString();

        // 處理 Base64 圖檔
        if (param.ImageBase64 != null)
        {
            // 找到 Base64 起始位置
            var base64Index = param.ImageBase64.IndexOf(";base64,");
            if (base64Index >= 0)
            {
                param.ImageBase64 = param.ImageBase64.Substring(base64Index + ";base64,".Length);
            }

            // 將 Base64 轉成 byte[]
            byte[] imageBytes = Convert.FromBase64String(param.ImageBase64);
            string fileExtension = param.ImageFileType.StartsWith(".") ? param.ImageFileType : $".{param.ImageFileType}";
            string tempFileName = $"{fileName}{fileExtension}";
            var tempFilePath = Path.Combine(FileStorageBasePath, tempFileName);

            // 儲存到臨時檔案
            await System.IO.File.WriteAllBytesAsync(tempFilePath, imageBytes);

            // 設定目標儲存路徑
            var storagePath = Path.Combine(FileStorageBasePath, "SystemImages");
            if (!Directory.Exists(storagePath))
            {
                Directory.CreateDirectory(storagePath);
            }

            // 目標檔案路徑（包含副檔名）
            var destinationFilePath = Path.Combine(storagePath, $"{fileName}{fileExtension}");

            // 複製檔案到目標路徑
            System.IO.File.Copy(tempFilePath, destinationFilePath, overwrite: true);

            // 設定返回的路徑
            planeMapPath = $"/FileStorage/SystemImages/{fileName}{fileExtension}";

            // 刪除臨時檔案
            System.IO.File.Delete(tempFilePath);
        }

        // 資料庫操作部分保持不變        
        SysParameters SysPara = _dataAccessService.Fetch<SysParameters>(e => e.ParaCode == param.ParaCode).AsTracking().First();
        GlobalSysPara? globalSysPara = _dataAccessService.Fetch<GlobalSysPara>(e => e.AppCode == appCode && e.ParaCode == param.ParaCode).AsTracking().FirstOrDefault();

        if (globalSysPara == null)
        {
            GlobalSysPara createGlobalSysPara = new GlobalSysPara
            {
                AppCode = _user.AppCode,
                ParaCode = SysPara.ParaCode,
                ParaValue = param.ImageBase64 != null ? planeMapPath : param.ParaValue,
                ParaType = SysPara.ParaType,
                CreateDate = DateTime.Now,
                CreateUserAccount = _user.Account
            };
            await _dataAccessService.CreateAsync<GlobalSysPara>(createGlobalSysPara);
        }
        else
        {
            List<string> updateField = new List<string> { "ParaValue", "ModifyDate", "ModifyUserAccount" };
            globalSysPara.ParaValue = param.ImageBase64 != null ? planeMapPath : param.ParaValue;
            globalSysPara.ModifyDate = DateTime.Now;
            globalSysPara.ModifyUserAccount = _user.Account;
            await _dataAccessService.UpdateAsync<GlobalSysPara>(globalSysPara, updateField.ToArray());
        }

        _dataAccessService.BeginTransaction();
        await _dataAccessService.CommitAsync();

        _logService.Logging("info", logActionName, requestUUID, "system parameter update done.");

        return Ok(new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = true
        });
    }


    /// <summary>
    /// 刪除SysPara參數
    /// </summary>
    /// <returns></returns>
    [HttpDelete("sysPara")]
    public async Task<IActionResult> DeleteParameter([FromBody, RequestNotNullOrEmpty(ErrorMessage = Constants.ErrorCode.RequestNullOrEmpty)] List<DeleteParaCode> deleteDatas)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        _dataAccessService.BeginTransaction();

        foreach (var deleteData in deleteDatas)
        {
            if (deleteData.ParaCode == "SystemLogo")
            {

                var systemParam = _dataAccessService.Fetch<SysParameters>(e => e.ParaCode == deleteData.ParaCode)
                                .FirstOrDefault();
                var globalParam = _dataAccessService.Fetch<GlobalSysPara>(e => e.AppCode == _user.AppCode && e.ParaCode == deleteData.ParaCode && e.AppCode == _user.AppCode)
                .FirstOrDefault();
                var pathData = globalParam?.ParaValue ?? systemParam?.ParaValue ?? string.Empty;
                if (pathData != null)
                {
                    string oldFilePath = pathData.Replace("/", "\\")[1..pathData.Length];
                    if (oldFilePath != null && System.IO.File.Exists(oldFilePath))
                    {
                        System.IO.File.Delete(oldFilePath);
                        _logService.Logging("info", logActionName, requestUUID, $"Deleted image file: {oldFilePath}");
                    }
                }

            }
            // 刪除系統參數
            await _dataAccessService.DeleteAsync<GlobalSysPara>(e => e.AppCode == _user.AppCode && e.ParaCode == deleteData.ParaCode);
        }

        await _dataAccessService.CommitAsync();

        _logService.Logging("info", logActionName, requestUUID, "GlobalSysPara Data delete done.");

        // 判斷是否有錯誤
        // bool deleteResult = deleteStationErrors.Count == 0;

        return Ok(new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = true
        });
    }
    /// <summary>
    /// 取得系統目前的 Locale 內容
    /// </summary>
    /// <returns></returns>
    [HttpGet("locales")]
    public async Task<IActionResult> GetAllLocale()
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        ReturnModel returnModel;

        var result = await _dataAccessService.Fetch<Locale>().ToListAsync();

        returnModel = new ReturnModel
        {
            requestUUID = requestUUID,
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = result
        };

        return Ok(returnModel);
    }
}
